# FSLE计算项目

基于海洋高度计数据计算有限时间李雅普诺夫指数（Finite-Size Lyapunov Exponent, FSLE）的Python项目。

## 项目结构

```
cal_fsle/
├── config/                    # 配置模块
│   ├── __init__.py
│   ├── setting.yaml          # 主配置文件
│   └── config_loader.py      # 配置加载器
├── utils/                     # 工具模块
│   ├── __init__.py
│   ├── time_utils.py         # 时间处理工具
│   ├── nc_merger.py          # NC文件合并工具
│   ├── fsle_calculator.py    # FSLE计算核心
│   └── logger_utils.py       # 日志工具
├── logs/                      # 日志目录
├── main.py                    # 主程序入口
├── test.py                    # 原始测试文件
└── README.md                  # 项目说明
```

## 主要功能

### 1. NC文件合并
- 基于当前UTC时间，自动合并当天及前119天的NC文件
- 输入文件模式：`/data2/eddy/global/{date}/input/nrt_global_allsat_phy_l4_{date}_{date}.nc`
- 输出文件：`/data2/eddy/NRT_ADT.nc`
- 确保输出包含：`adt`、`longitude`、`latitude`、`time`参数

### 2. FSLE计算
- 基于合并的NRT_ADT.nc文件计算FSLE
- 使用py_eddy_tracker库进行粒子平流
- 支持可配置的计算参数
- 输出标准NetCDF格式文件

### 3. 日志系统
- 按日期分割的日志文件：`./logs/main_YYYYMMDD.log`
- 结构化日志记录，包含详细的执行信息
- 支持控制台和文件双重输出

## 依赖项

```
xarray
numpy
pandas
numba
py_eddy_tracker
PyYAML
```

## 安装依赖

```bash
pip install xarray numpy pandas numba py_eddy_tracker PyYAML
```

## 使用方法

### 基本运行
```bash
python main.py
```

### 检查依赖项
```bash
python main.py --check-deps
```

### 显示配置
```bash
python main.py --show-config
```

### 显示帮助
```bash
python main.py --help
```

## 配置说明

主要配置文件：`config/setting.yaml`

### 数据路径配置
```yaml
data_paths:
  input_pattern: "/data2/eddy/global/{date}/input/nrt_global_allsat_phy_l4_{date}_{date}.nc"
  output_file: "/data2/eddy/NRT_ADT.nc"
  fsle_output: "/data2/wht_test/fsle_{date}.nc"
```

### FSLE计算参数
```yaml
fsle_params:
  step_grid_out: 0.04          # 输出网格步长（度）
  dist_init: 0.02              # 初始分离距离（度）
  dist_max: 0.6                # 最大分离距离（度）
  t0: 120                      # 起始时间索引
  time_step_by_days: 5         # 每天的时间步数
  nb_days: 115                 # 最大平流时间（天）
  backward: true               # 是否向后平流
```

## 输出文件

### 合并的NC文件
- 路径：`/data2/eddy/NRT_ADT.nc`
- 包含变量：`adt`, `longitude`, `latitude`, `time`
- 时间范围：当前日期及前119天

### FSLE结果文件
- 路径：`/data2/wht_test/fsle_{YYYYMMDD}.nc`
- 包含变量：
  - `fsle`: 有限时间李雅普诺夫指数 (day⁻¹)
  - `theta`: FSLE脊线方向角 (degrees)
  - `lon`: 经度 (degrees_east)
  - `lat`: 纬度 (degrees_north)

## 日志文件

日志文件保存在 `./logs/main_YYYYMMDD.log`，包含：
- 程序执行状态
- 文件处理进度
- 错误和警告信息
- 计算参数和结果统计

## 注意事项

1. 确保输入数据路径存在且可访问
2. 确保输出目录有写入权限
3. FSLE计算可能需要较长时间，请耐心等待
4. 如遇到内存不足，可调整网格分辨率参数
5. 建议在计算前检查依赖项是否完整安装
