"""
配置文件加载器
负责加载和解析YAML配置文件
"""
import os
import yaml
from typing import Dict, Any


class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径，默认为../config/setting.yaml
        """
        if config_path is None:
            # 获取当前文件所在目录的父目录，然后拼接config/setting.yaml
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            config_path = os.path.join(parent_dir, 'config', 'setting.yaml')
        
        self.config_path = config_path
        self._config = None
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if self._config is None:
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f)
            except FileNotFoundError:
                raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
            except yaml.YAMLError as e:
                raise ValueError(f"配置文件格式错误: {e}")
        
        return self._config
    
    def get(self, key: str, default=None):
        """
        获取配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'fsle_params.step_grid_out'
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.load_config()
        
        # 支持嵌套键访问
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_paths(self) -> Dict[str, str]:
        """获取数据路径配置"""
        return self.get('data_paths', {})
    
    def get_fsle_params(self) -> Dict[str, Any]:
        """获取FSLE计算参数"""
        return self.get('fsle_params', {})
    
    def get_time_config(self) -> Dict[str, Any]:
        """获取时间配置"""
        return self.get('time_config', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_grid_config(self) -> Dict[str, Any]:
        """获取网格配置"""
        return self.get('grid_config', {})


# 创建全局配置实例
config = ConfigLoader()
