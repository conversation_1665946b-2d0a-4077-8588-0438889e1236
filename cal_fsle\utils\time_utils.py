"""
时间处理工具模块
负责处理UTC时间、日期计算和文件路径生成
"""
import os
from datetime import datetime, timedelta
from typing import List, Tuple
from utils.config_loader import config


class TimeUtils:
    """时间处理工具类"""
    
    @staticmethod
    def get_current_utc() -> datetime:
        """
        获取当前UTC时间
        
        Returns:
            当前UTC时间
        """
        return datetime.utcnow()
    
    @staticmethod
    def get_date_range(end_date: datetime = None, days_back: int = None) -> List[datetime]:
        """
        获取日期范围
        
        Args:
            end_date: 结束日期，默认为当前UTC时间
            days_back: 向前追溯天数，默认从配置文件读取
            
        Returns:
            日期列表
        """
        if end_date is None:
            end_date = TimeUtils.get_current_utc()
        
        if days_back is None:
            days_back = config.get('time_config.days_back', 119)
        
        # 生成日期范围（包含当天和前days_back天）
        dates = []
        for i in range(days_back + 1):
            date = end_date - timedelta(days=i)
            dates.append(date)
        
        # 按时间顺序排序（最早的在前）
        dates.reverse()
        return dates
    
    @staticmethod
    def format_date(date: datetime, format_str: str = None) -> str:
        """
        格式化日期（兼容旧接口）

        Args:
            date: 日期对象
            format_str: 格式字符串，默认从配置文件读取文件名格式

        Returns:
            格式化后的日期字符串
        """
        if format_str is None:
            format_str = config.get('time_config.date_format_file', '%Y%m%d')

        return date.strftime(format_str)

    @staticmethod
    def format_date_for_file(date: datetime) -> str:
        """
        格式化日期用于文件名

        Args:
            date: 日期对象

        Returns:
            文件名格式的日期字符串 (如: 20250407)
        """
        format_str = config.get('time_config.date_format_file', '%Y%m%d')
        return date.strftime(format_str)

    @staticmethod
    def format_date_for_dir(date: datetime) -> str:
        """
        格式化日期用于目录名

        Args:
            date: 日期对象

        Returns:
            目录名格式的日期字符串 (如: 2025_04_07)
        """
        format_str = config.get('time_config.date_format_dir', '%Y_%m_%d')
        return date.strftime(format_str)
    
    @staticmethod
    def generate_input_file_paths(dates: List[datetime] = None) -> List[str]:
        """
        生成输入文件路径列表

        Args:
            dates: 日期列表，默认为当前日期及前119天

        Returns:
            文件路径列表
        """
        if dates is None:
            dates = TimeUtils.get_date_range()

        input_pattern = config.get('data_paths.input_pattern',
                                 '/data2/eddy/global/{date_dir}/input/nrt_global_allsat_phy_l4_{date_file}_{date_file}.nc')

        file_paths = []
        for date in dates:
            date_dir = TimeUtils.format_date_for_dir(date)    # 2025_04_07 格式
            date_file = TimeUtils.format_date_for_file(date)  # 20250407 格式
            file_path = input_pattern.format(date_dir=date_dir, date_file=date_file)
            file_paths.append(file_path)

        return file_paths
    
    @staticmethod
    def check_files_exist(file_paths: List[str]) -> Tuple[List[str], List[str]]:
        """
        检查文件是否存在
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            (存在的文件列表, 不存在的文件列表)
        """
        existing_files = []
        missing_files = []
        
        for file_path in file_paths:
            if os.path.exists(file_path):
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        return existing_files, missing_files
    
    @staticmethod
    def get_output_file_path() -> str:
        """
        获取输出文件路径
        
        Returns:
            输出文件路径
        """
        return config.get('data_paths.output_file', '/data2/eddy/NRT_ADT.nc')
    
    @staticmethod
    def get_fsle_output_path(date: datetime = None) -> str:
        """
        获取FSLE输出文件路径

        Args:
            date: 日期，默认为当前UTC时间

        Returns:
            FSLE输出文件路径
        """
        if date is None:
            date = TimeUtils.get_current_utc()

        date_file = TimeUtils.format_date_for_file(date)  # 20250407 格式
        output_pattern = config.get('data_paths.fsle_output',
                                  '/data2/zxhy/{date_file}/fsle/fsle.nc')

        return output_pattern.format(date_file=date_file)
