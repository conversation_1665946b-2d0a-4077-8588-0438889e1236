"""
日志工具模块
负责配置和管理日志系统
"""
import os
import logging
from datetime import datetime
from typing import Optional
from utils.config_loader import config


class LoggerUtils:
    """日志工具类"""
    
    _loggers = {}  # 存储已创建的logger实例
    
    @staticmethod
    def setup_logger(name: str = 'cal_fsle', 
                    log_file: Optional[str] = None,
                    level: Optional[str] = None) -> logging.Logger:
        """
        设置日志器
        
        Args:
            name: 日志器名称
            log_file: 日志文件路径，默认为当前日期的日志文件
            level: 日志级别，默认从配置文件读取
            
        Returns:
            配置好的日志器
        """
        # 如果已经创建过该名称的logger，直接返回
        if name in LoggerUtils._loggers:
            return LoggerUtils._loggers[name]
        
        # 创建logger
        logger = logging.getLogger(name)
        
        # 设置日志级别
        if level is None:
            level = config.get('logging.level', 'INFO')
        logger.setLevel(getattr(logging, level.upper()))
        
        # 清除已有的处理器（避免重复添加）
        logger.handlers.clear()
        
        # 创建格式器
        log_format = config.get('logging.format', 
                               '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file is None:
            log_file = LoggerUtils.get_log_file_path()
        
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 存储logger实例
        LoggerUtils._loggers[name] = logger
        
        return logger
    
    @staticmethod
    def get_log_file_path(date: Optional[datetime] = None) -> str:
        """
        获取日志文件路径
        
        Args:
            date: 日期，默认为当前日期
            
        Returns:
            日志文件路径
        """
        if date is None:
            date = datetime.now()
        
        log_dir = config.get('logging.log_dir', './logs')
        date_str = date.strftime('%Y%m%d')
        log_filename = f'main_{date_str}.log'
        
        return os.path.join(log_dir, log_filename)
    
    @staticmethod
    def get_logger(name: str = 'cal_fsle') -> logging.Logger:
        """
        获取日志器实例
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        if name in LoggerUtils._loggers:
            return LoggerUtils._loggers[name]
        else:
            return LoggerUtils.setup_logger(name)
    
    @staticmethod
    def log_function_start(logger: logging.Logger, function_name: str, **kwargs):
        """
        记录函数开始执行
        
        Args:
            logger: 日志器
            function_name: 函数名
            **kwargs: 函数参数
        """
        params_str = ', '.join([f'{k}={v}' for k, v in kwargs.items()])
        logger.info(f"开始执行 {function_name}({params_str})")
    
    @staticmethod
    def log_function_end(logger: logging.Logger, function_name: str, success: bool = True, **kwargs):
        """
        记录函数执行结束
        
        Args:
            logger: 日志器
            function_name: 函数名
            success: 是否成功
            **kwargs: 额外信息
        """
        status = "成功" if success else "失败"
        extra_info = ', '.join([f'{k}={v}' for k, v in kwargs.items()])
        if extra_info:
            logger.info(f"{function_name} 执行{status} - {extra_info}")
        else:
            logger.info(f"{function_name} 执行{status}")
    
    @staticmethod
    def log_progress(logger: logging.Logger, current: int, total: int, description: str = "处理进度"):
        """
        记录进度信息
        
        Args:
            logger: 日志器
            current: 当前进度
            total: 总数
            description: 描述信息
        """
        percentage = (current / total) * 100 if total > 0 else 0
        logger.info(f"{description}: {current}/{total} ({percentage:.1f}%)")
