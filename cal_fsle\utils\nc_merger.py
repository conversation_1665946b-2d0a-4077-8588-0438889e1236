"""
NetCDF文件合并工具模块
负责合并多个NC文件到单一文件
"""
import os
import xarray as xr
import numpy as np
from typing import List, Optional
from utils.logger_utils import LoggerUtils
from utils.time_utils import TimeUtils


class NCMerger:
    """NetCDF文件合并器"""
    
    def __init__(self, logger_name: str = 'nc_merger'):
        """
        初始化合并器
        
        Args:
            logger_name: 日志器名称
        """
        self.logger = LoggerUtils.get_logger(logger_name)
    
    def merge_files(self, 
                   input_files: List[str], 
                   output_file: str,
                   required_vars: List[str] = None) -> bool:
        """
        合并多个NC文件
        
        Args:
            input_files: 输入文件路径列表
            output_file: 输出文件路径
            required_vars: 必需的变量列表，默认为['adt', 'longitude', 'latitude', 'time']
            
        Returns:
            是否成功
        """
        if required_vars is None:
            required_vars = ['adt', 'longitude', 'latitude', 'time']
        
        LoggerUtils.log_function_start(self.logger, 'merge_files', 
                                     input_count=len(input_files), 
                                     output_file=output_file)
        
        try:
            # 检查输入文件
            existing_files, missing_files = TimeUtils.check_files_exist(input_files)
            
            if missing_files:
                self.logger.warning(f"发现 {len(missing_files)} 个缺失文件:")
                for file in missing_files[:5]:  # 只显示前5个
                    self.logger.warning(f"  - {file}")
                if len(missing_files) > 5:
                    self.logger.warning(f"  ... 还有 {len(missing_files) - 5} 个文件")
            
            if not existing_files:
                self.logger.error("没有找到任何有效的输入文件")
                return False
            
            self.logger.info(f"找到 {len(existing_files)} 个有效文件，开始合并")
            
            # 读取并合并数据
            datasets = []
            for i, file_path in enumerate(existing_files):
                try:
                    LoggerUtils.log_progress(self.logger, i + 1, len(existing_files), "读取文件")
                    
                    ds = xr.open_dataset(file_path)
                    
                    # 检查必需变量
                    missing_vars = [var for var in required_vars if var not in ds.variables]
                    if missing_vars:
                        self.logger.warning(f"文件 {file_path} 缺少变量: {missing_vars}")
                        continue
                    
                    # 只保留必需的变量
                    ds_filtered = ds[required_vars]
                    datasets.append(ds_filtered)
                    
                except Exception as e:
                    self.logger.error(f"读取文件 {file_path} 失败: {e}")
                    continue
            
            if not datasets:
                self.logger.error("没有成功读取任何数据集")
                return False
            
            self.logger.info(f"成功读取 {len(datasets)} 个数据集，开始合并")
            
            # 沿时间维度合并
            merged_ds = xr.concat(datasets, dim='time')
            
            # 按时间排序
            merged_ds = merged_ds.sortby('time')
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存合并后的数据
            self.logger.info(f"保存合并数据到: {output_file}")
            merged_ds.to_netcdf(output_file)
            
            # 记录合并信息
            time_range = f"{merged_ds.time.values[0]} 到 {merged_ds.time.values[-1]}"
            data_shape = {var: merged_ds[var].shape for var in required_vars}
            
            LoggerUtils.log_function_end(self.logger, 'merge_files', True,
                                       time_range=time_range,
                                       data_shape=data_shape)
            
            return True
            
        except Exception as e:
            self.logger.error(f"合并文件时发生错误: {e}")
            LoggerUtils.log_function_end(self.logger, 'merge_files', False, error=str(e))
            return False
    
    def merge_daily_files(self, days_back: int = None, output_file: str = None) -> bool:
        """
        合并当天及前N天的日常文件
        
        Args:
            days_back: 向前追溯天数，默认从配置读取
            output_file: 输出文件路径，默认从配置读取
            
        Returns:
            是否成功
        """
        LoggerUtils.log_function_start(self.logger, 'merge_daily_files', 
                                     days_back=days_back, output_file=output_file)
        
        try:
            # 生成输入文件路径
            input_files = TimeUtils.generate_input_file_paths()
            
            if output_file is None:
                output_file = TimeUtils.get_output_file_path()
            
            # 执行合并
            success = self.merge_files(input_files, output_file)
            
            LoggerUtils.log_function_end(self.logger, 'merge_daily_files', success)
            return success
            
        except Exception as e:
            self.logger.error(f"合并日常文件时发生错误: {e}")
            LoggerUtils.log_function_end(self.logger, 'merge_daily_files', False, error=str(e))
            return False
