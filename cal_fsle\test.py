from matplotlib import pyplot as plt
from numba import njit
from numpy import arange, arctan2, empty, isnan, log, ma, meshgrid, ones, pi, zeros

from py_eddy_tracker import start_logger
from py_eddy_tracker.data import get_demo_path
from py_eddy_tracker.dataset.grid import GridCollection, RegularGridDataset

start_logger().setLevel("ERROR")
c = GridCollection.from_netcdf_cube(
    get_demo_path("/data2/eddy/NRT_ADT.nc"),
    "longitude",
    "latitude",
    "time",
    # To create U/V variable
    heigth="adt",
)

@njit(cache=True, fastmath=True)
def check_p(x, y, flse, theta, m_set, m, dt, dist_init=0.02, dist_max=0.6):
    """
    Check if distance between eastern or northern particle to center particle is bigger than `dist_max`
    """
    nb_p = x.shape[0] // 3
    delta = dist_max**2
    for i in range(nb_p):
        i0 = i * 3
        i_n = i0 + 1
        i_e = i0 + 2
        # If particle already set, we skip
        if m[i0] or m[i_n] or m[i_e]:
            continue
        # Distance with north
        dxn, dyn = x[i0] - x[i_n], y[i0] - y[i_n]
        dn = dxn**2 + dyn**2
        # Distance with east
        dxe, dye = x[i0] - x[i_e], y[i0] - y[i_e]
        de = dxe**2 + dye**2

        if dn >= delta or de >= delta:
            s1 = dn + de
            at1 = 2 * (dxe * dxn + dye * dyn)
            at2 = de - dn
            s2 = ((dxn + dye) ** 2 + (dxe - dyn) ** 2) * (
                (dxn - dye) ** 2 + (dxe + dyn) ** 2
            )
            flse[i] = 1 / (2 * dt) * log(1 / (2 * dist_init**2) * (s1 + s2**0.5))
            theta[i] = arctan2(at1, at2 + s2) * 180 / pi
            # To know where value are set
            m_set[i] = False
            # To stop particle advection
            m[i0], m[i_n], m[i_e] = True, True, True


@njit(cache=True)
def build_triplet(x, y, step=0.02):
    """
    Triplet building for each position we add east and north point with defined step
    """
    nb_x = x.shape[0]
    x_ = empty(nb_x * 3, dtype=x.dtype)
    y_ = empty(nb_x * 3, dtype=y.dtype)
    for i in range(nb_x):
        i0 = i * 3
        i_n, i_e = i0 + 1, i0 + 2
        x__, y__ = x[i], y[i]
        x_[i0], y_[i0] = x__, y__
        x_[i_n], y_[i_n] = x__, y__ + step
        x_[i_e], y_[i_e] = x__ + step, y__
    return x_, y_

# Step in degrees for ouput
step_grid_out = 1 / 25.0
# Initial separation in degrees
dist_init = 1 / 50.0
# Final separation in degrees
dist_max = 6 / 10.0
# Time of start
t0 = 120
# Number of time step by days
time_step_by_days = 5
# Maximal time of advection
# Here we limit because our data cube cover only 3 month
nb_days = 115
# Backward or forward
backward = True

x0_, y0_ = -180, -90
lon_p = arange(x0_, 180, step_grid_out)
lat_p = arange(y0_, 90, step_grid_out)
y0, x0 = meshgrid(lat_p, lon_p)
grid_shape = x0.shape
x0, y0 = x0.reshape(-1), y0.reshape(-1)
# Identify all particle not on land
m = ~isnan(c[t0].interp("adt", x0, y0))
x0, y0 = x0[m], y0[m]

# Array to compute fsle
fsle = zeros(x0.shape[0], dtype="f4")
theta = zeros(x0.shape[0], dtype="f4")
mask = ones(x0.shape[0], dtype="f4")
x, y = build_triplet(x0, y0, dist_init)
used = zeros(x.shape[0], dtype="bool")

# advection generator
kw = dict(
    t_init=t0, nb_step=1, backward=backward, mask_particule=used, u_name="u", v_name="v"
)
p = c.advect(x, y, time_step=86400 / time_step_by_days, **kw)

# We check at each step of advection if particle distance is over `dist_max`
for i in range(time_step_by_days * nb_days):
    t, xt, yt = p.__next__()
    dt = t / 86400.0 - t0
    check_p(xt, yt, fsle, theta, mask, used, dt, dist_max=dist_max, dist_init=dist_init)

# Get index with original_position
i = ((x0 - x0_) / step_grid_out).astype("i4")
j = ((y0 - y0_) / step_grid_out).astype("i4")
fsle_ = empty(grid_shape, dtype="f4")
theta_ = empty(grid_shape, dtype="f4")
mask_ = ones(grid_shape, dtype="bool")
fsle_[i, j] = fsle
theta_[i, j] = theta
mask_[i, j] = mask
# Create a grid object
fsle_custom = RegularGridDataset.with_array(
    coordinates=("lon", "lat"),
    datas=dict(
        fsle=ma.array(fsle_, mask=mask_),
        theta=ma.array(theta_, mask=mask_),
        lon=lon_p,
        lat=lat_p,
    ),
    centered=True,
)

import xarray as xr
import numpy as np
import pandas as pd

# 提取 fsle_custom 中的数据
fsle_data = fsle_custom.grid("fsle")
theta_data = fsle_custom.grid("theta")
lon_data = fsle_custom.grid("lon")
lat_data = fsle_custom.grid("lat")

# 创建 xarray 数据集
ds = xr.Dataset(
    {
        "fsle": (("lon", "lat"), fsle_data.filled(np.nan)),  # 将掩码值转为 NaN
        "theta": (("lon", "lat"), theta_data.filled(np.nan)),
    },
    coords={
        "lon": lon_data,
        "lat": lat_data,
    }
)

# 添加变量属性(需要添加time 属性)
ds.fsle.attrs = {
    "long_name": "Finite-Size Lyapunov Exponent",
    "units": "day⁻¹",
}
ds.theta.attrs = {
    "long_name": "Orientation angle of FSLE ridges",
    "units": "radians",
}
ds.lon.attrs = {"long_name": "Longitude", "units": "degrees_east"}
ds.lat.attrs = {"long_name": "Latitude", "units": "degrees_north"}

# 设置全局属性（可选）
ds.attrs = {
    "title": "FSLE Data",
    "source": "AVISO",
    "creation_date": pd.Timestamp.now().strftime("%Y-%m-%d"),
}

# 导出为 NetCDF 文件
output_path = "/data2/wht_test/fsle.nc"
ds.to_netcdf(output_path)