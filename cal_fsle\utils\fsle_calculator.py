"""
FSLE计算核心模块
基于test.py的算法实现FSLE计算
"""
import os
import xarray as xr
import numpy as np
import pandas as pd
from numba import njit
from numpy import arange, arctan2, empty, isnan, log, ma, meshgrid, ones, pi, zeros
from py_eddy_tracker import start_logger
from py_eddy_tracker.dataset.grid import GridCollection, RegularGridDataset
from typing import Optional, Tuple
from utils.logger_utils import LoggerUtils
from utils.time_utils import TimeUtils
from utils.config_loader import config


class FSLECalculator:
    """FSLE计算器"""
    
    def __init__(self, logger_name: str = 'fsle_calculator'):
        """
        初始化FSLE计算器
        
        Args:
            logger_name: 日志器名称
        """
        self.logger = LoggerUtils.get_logger(logger_name)
        # 设置py_eddy_tracker日志级别为ERROR以减少输出
        start_logger().setLevel("ERROR")
    
    @staticmethod
    @njit(cache=True, fastmath=True)
    def check_p(x, y, flse, theta, m_set, m, dt, dist_init=0.02, dist_max=0.6):
        """
        检查东向或北向粒子到中心粒子的距离是否大于dist_max
        
        Args:
            x, y: 粒子位置
            flse: FSLE值数组
            theta: 角度数组
            m_set: 设置掩码
            m: 使用掩码
            dt: 时间差
            dist_init: 初始距离
            dist_max: 最大距离
        """
        nb_p = x.shape[0] // 3
        delta = dist_max**2
        for i in range(nb_p):
            i0 = i * 3
            i_n = i0 + 1
            i_e = i0 + 2
            # 如果粒子已经设置，跳过
            if m[i0] or m[i_n] or m[i_e]:
                continue
            # 与北向的距离
            dxn, dyn = x[i0] - x[i_n], y[i0] - y[i_n]
            dn = dxn**2 + dyn**2
            # 与东向的距离
            dxe, dye = x[i0] - x[i_e], y[i0] - y[i_e]
            de = dxe**2 + dye**2

            if dn >= delta or de >= delta:
                s1 = dn + de
                at1 = 2 * (dxe * dxn + dye * dyn)
                at2 = de - dn
                s2 = ((dxn + dye) ** 2 + (dxe - dyn) ** 2) * (
                    (dxn - dye) ** 2 + (dxe + dyn) ** 2
                )
                flse[i] = 1 / (2 * dt) * log(1 / (2 * dist_init**2) * (s1 + s2**0.5))
                theta[i] = arctan2(at1, at2 + s2) * 180 / pi
                # 标记值已设置
                m_set[i] = False
                # 停止粒子平流
                m[i0], m[i_n], m[i_e] = True, True, True

    @staticmethod
    @njit(cache=True)
    def build_triplet(x, y, step=0.02):
        """
        为每个位置构建三元组，添加东向和北向点
        
        Args:
            x, y: 原始位置
            step: 步长
            
        Returns:
            扩展后的位置数组
        """
        nb_x = x.shape[0]
        x_ = empty(nb_x * 3, dtype=x.dtype)
        y_ = empty(nb_x * 3, dtype=y.dtype)
        for i in range(nb_x):
            i0 = i * 3
            i_n, i_e = i0 + 1, i0 + 2
            x__, y__ = x[i], y[i]
            x_[i0], y_[i0] = x__, y__
            x_[i_n], y_[i_n] = x__, y__ + step
            x_[i_e], y_[i_e] = x__ + step, y__
        return x_, y_
    
    def calculate_fsle(self, 
                      input_file: str, 
                      output_file: str = None,
                      fsle_params: dict = None) -> bool:
        """
        计算FSLE
        
        Args:
            input_file: 输入NC文件路径
            output_file: 输出文件路径，默认从配置或时间生成
            fsle_params: FSLE计算参数，默认从配置读取
            
        Returns:
            是否成功
        """
        LoggerUtils.log_function_start(self.logger, 'calculate_fsle', 
                                     input_file=input_file, output_file=output_file)
        
        try:
            # 检查输入文件
            if not os.path.exists(input_file):
                self.logger.error(f"输入文件不存在: {input_file}")
                return False
            
            # 获取参数
            if fsle_params is None:
                fsle_params = config.get_fsle_params()
            
            if output_file is None:
                output_file = TimeUtils.get_fsle_output_path()
            
            # 提取参数
            step_grid_out = fsle_params.get('step_grid_out', 0.04)
            dist_init = fsle_params.get('dist_init', 0.02)
            dist_max = fsle_params.get('dist_max', 0.6)
            t0 = fsle_params.get('t0', 120)
            time_step_by_days = fsle_params.get('time_step_by_days', 5)
            nb_days = fsle_params.get('nb_days', 115)
            backward = fsle_params.get('backward', True)
            
            self.logger.info(f"FSLE计算参数: step_grid_out={step_grid_out}, dist_init={dist_init}, "
                           f"dist_max={dist_max}, t0={t0}, nb_days={nb_days}")
            
            # 加载数据
            self.logger.info("加载GridCollection数据...")
            c = GridCollection.from_netcdf_cube(
                input_file,
                "longitude",
                "latitude", 
                "time",
                heigth="adt",
            )
            
            # 设置网格
            grid_config = config.get_grid_config()
            x0_, y0_ = grid_config.get('lon_min', -180), grid_config.get('lat_min', -90)
            lon_max, lat_max = grid_config.get('lon_max', 180), grid_config.get('lat_max', 90)
            
            lon_p = arange(x0_, lon_max, step_grid_out)
            lat_p = arange(y0_, lat_max, step_grid_out)
            y0, x0 = meshgrid(lat_p, lon_p)
            grid_shape = x0.shape
            x0, y0 = x0.reshape(-1), y0.reshape(-1)
            
            # 识别非陆地粒子
            self.logger.info("识别非陆地粒子...")
            m = ~isnan(c[t0].interp("adt", x0, y0))
            x0, y0 = x0[m], y0[m]
            
            self.logger.info(f"有效粒子数量: {len(x0)}")
            
            # 初始化FSLE计算数组
            fsle = zeros(x0.shape[0], dtype="f4")
            theta = zeros(x0.shape[0], dtype="f4")
            mask = ones(x0.shape[0], dtype="f4")
            x, y = self.build_triplet(x0, y0, dist_init)
            used = zeros(x.shape[0], dtype="bool")
            
            # 平流生成器
            self.logger.info("开始粒子平流计算...")
            kw = dict(
                t_init=t0, nb_step=1, backward=backward, 
                mask_particule=used, u_name="u", v_name="v"
            )
            p = c.advect(x, y, time_step=86400 / time_step_by_days, **kw)
            
            # 在每个平流步骤检查粒子距离
            total_steps = time_step_by_days * nb_days
            for i in range(total_steps):
                if i % (total_steps // 10) == 0:  # 每10%记录一次进度
                    LoggerUtils.log_progress(self.logger, i, total_steps, "FSLE计算进度")
                
                t, xt, yt = p.__next__()
                dt = t / 86400.0 - t0
                self.check_p(xt, yt, fsle, theta, mask, used, dt, 
                           dist_max=dist_max, dist_init=dist_init)
            
            self.logger.info("粒子平流计算完成，生成输出网格...")
            
            # 获取原始位置索引
            i = ((x0 - x0_) / step_grid_out).astype("i4")
            j = ((y0 - y0_) / step_grid_out).astype("i4")
            fsle_ = empty(grid_shape, dtype="f4")
            theta_ = empty(grid_shape, dtype="f4")
            mask_ = ones(grid_shape, dtype="bool")
            fsle_[i, j] = fsle
            theta_[i, j] = theta
            mask_[i, j] = mask
            
            # 创建网格对象
            fsle_custom = RegularGridDataset.with_array(
                coordinates=("lon", "lat"),
                datas=dict(
                    fsle=ma.array(fsle_, mask=mask_),
                    theta=ma.array(theta_, mask=mask_),
                    lon=lon_p,
                    lat=lat_p,
                ),
                centered=True,
            )
            
            # 转换为xarray数据集
            self.logger.info("转换为xarray格式并保存...")
            fsle_data = fsle_custom.grid("fsle")
            theta_data = fsle_custom.grid("theta")
            lon_data = fsle_custom.grid("lon")
            lat_data = fsle_custom.grid("lat")
            
            ds = xr.Dataset(
                {
                    "fsle": (("lon", "lat"), fsle_data.filled(np.nan)),
                    "theta": (("lon", "lat"), theta_data.filled(np.nan)),
                },
                coords={
                    "lon": lon_data,
                    "lat": lat_data,
                }
            )
            
            # 添加属性
            ds.fsle.attrs = {
                "long_name": "Finite-Size Lyapunov Exponent",
                "units": "day⁻¹",
            }
            ds.theta.attrs = {
                "long_name": "Orientation angle of FSLE ridges", 
                "units": "degrees",
            }
            ds.lon.attrs = {"long_name": "Longitude", "units": "degrees_east"}
            ds.lat.attrs = {"long_name": "Latitude", "units": "degrees_north"}
            
            # 设置全局属性
            ds.attrs = {
                "title": "FSLE Data",
                "source": "AVISO",
                "creation_date": pd.Timestamp.now().strftime("%Y-%m-%d"),
                "input_file": input_file,
                "fsle_parameters": str(fsle_params)
            }
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存文件
            ds.to_netcdf(output_file)
            
            LoggerUtils.log_function_end(self.logger, 'calculate_fsle', True,
                                       output_file=output_file,
                                       data_shape=ds.dims)
            
            return True
            
        except Exception as e:
            self.logger.error(f"FSLE计算时发生错误: {e}")
            LoggerUtils.log_function_end(self.logger, 'calculate_fsle', False, error=str(e))
            return False
