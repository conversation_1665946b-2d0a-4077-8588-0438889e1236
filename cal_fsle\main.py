#!/usr/bin/env python3
"""
FSLE计算主程序
主要流程：
1. 检查当前UTC时间，基于当前UTC时间合并当天及前119天的nc文件
2. 基于合并的NRT_ADT.nc计算FSLE并输出
3. 日志保存到./logs/main_YYYYMMDD.log中
"""
import sys
import os
from datetime import datetime
from utils.logger_utils import LoggerUtils
from utils.time_utils import TimeUtils
from utils.nc_merger import NCMerger
from utils.fsle_calculator import FSLECalculator
from utils.config_loader import config


def main():
    """主函数"""
    # 设置主日志器
    logger = LoggerUtils.setup_logger('main')
    
    try:
        logger.info("=" * 60)
        logger.info("FSLE计算程序启动")
        logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
        
        # 第一步：检查当前UTC时间并合并NC文件
        logger.info("第一步：合并NC文件")
        logger.info("-" * 40)
        
        current_utc = TimeUtils.get_current_utc()
        logger.info(f"当前UTC时间: {current_utc.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建NC合并器
        merger = NCMerger('nc_merger')
        
        # 执行合并
        merge_success = merger.merge_daily_files()
        
        if not merge_success:
            logger.error("NC文件合并失败，程序终止")
            return 1
        
        logger.info("NC文件合并完成")
        
        # 第二步：计算FSLE
        logger.info("\n第二步：计算FSLE")
        logger.info("-" * 40)
        
        # 创建FSLE计算器
        calculator = FSLECalculator('fsle_calculator')
        
        # 获取输入和输出文件路径
        input_file = TimeUtils.get_output_file_path()
        output_file = TimeUtils.get_fsle_output_path(current_utc)
        
        logger.info(f"输入文件: {input_file}")
        logger.info(f"输出文件: {output_file}")
        
        # 执行FSLE计算
        fsle_success = calculator.calculate_fsle(input_file, output_file)
        
        if not fsle_success:
            logger.error("FSLE计算失败，程序终止")
            return 1
        
        logger.info("FSLE计算完成")
        
        # 程序完成
        logger.info("\n" + "=" * 60)
        logger.info("FSLE计算程序执行完成")
        logger.info(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"最终输出文件: {output_file}")
        logger.info("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
        return 130
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        logger.exception("详细错误信息:")
        return 1


def check_dependencies():
    """检查依赖项"""
    logger = LoggerUtils.get_logger('dependency_check')
    
    required_modules = [
        'xarray', 'numpy', 'pandas', 'numba', 
        'py_eddy_tracker', 'yaml'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.debug(f"模块 {module} 检查通过")
        except ImportError:
            missing_modules.append(module)
            logger.error(f"缺少必需模块: {module}")
    
    if missing_modules:
        logger.error(f"缺少以下必需模块: {', '.join(missing_modules)}")
        logger.error("请使用以下命令安装:")
        logger.error(f"pip install {' '.join(missing_modules)}")
        return False
    
    logger.info("所有依赖项检查通过")
    return True


def show_config():
    """显示当前配置"""
    logger = LoggerUtils.get_logger('config_display')
    
    logger.info("当前配置信息:")
    logger.info("-" * 30)
    
    # 数据路径配置
    data_paths = config.get_data_paths()
    logger.info("数据路径配置:")
    for key, value in data_paths.items():
        logger.info(f"  {key}: {value}")
    
    # FSLE参数配置
    fsle_params = config.get_fsle_params()
    logger.info("FSLE计算参数:")
    for key, value in fsle_params.items():
        logger.info(f"  {key}: {value}")
    
    # 时间配置
    time_config = config.get_time_config()
    logger.info("时间配置:")
    for key, value in time_config.items():
        logger.info(f"  {key}: {value}")


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--check-deps":
            # 检查依赖项
            if check_dependencies():
                sys.exit(0)
            else:
                sys.exit(1)
        elif sys.argv[1] == "--show-config":
            # 显示配置
            show_config()
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("FSLE计算程序")
            print("用法:")
            print("  python main.py              # 运行主程序")
            print("  python main.py --check-deps # 检查依赖项")
            print("  python main.py --show-config # 显示配置")
            print("  python main.py --help       # 显示帮助")
            sys.exit(0)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
